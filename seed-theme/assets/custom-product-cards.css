/* Custom Product Card Styles for Collection Pages */

/* Product Title Styling */
.custom-product-title {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  cursor: pointer !important;
  display: inline !important;
  font-family: Lato, sans-serif !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  height: auto !important;
  line-height: 24px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-decoration-color: rgb(74, 74, 74) !important;
  text-decoration-line: none !important;
  text-decoration-style: solid !important;
  text-decoration-thickness: auto !important;
  text-size-adjust: 100% !important;
  width: auto !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Price Container Layout */
.custom-price-container {
  display: flex !important;
  align-items: baseline !important;
  flex-wrap: wrap !important;
}

/* Sale Price Styling */
.custom-sale-price {
  box-sizing: border-box !important;
  color: rgb(252, 8, 8) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22.4px !important;
  margin-right: 5px !important;
  outline-color: rgb(252, 8, 8) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Code Text Styling */
.custom-code-text {
  background-color: rgba(230, 245, 252, 0.5) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 21px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  padding: 0px 1px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
  border-radius: 2px !important;
}

/* Old Price (Strikethrough) Styling */
.custom-price-container .old-price {
  color: rgb(147, 143, 156) !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 22.4px !important;
  text-decoration: line-through !important;
  margin-right: 5px !important;
}

/* Hide Sale Labels and View Options Buttons */
.product-card .s1lb .overlay-sale,
.product-card .link-btn,
.product-card .overlay-buy_button {
  display: none !important;
}

/* Hide any hover buttons or overlays */
.product-card:hover .overlay-buy_button,
.product-card:hover .link-btn {
  display: none !important;
}

/* Desktop Product Grid Adjustments - Smaller Cards */
@media (min-width: 1001px) {
  /* Reduce product card width for desktop */
  .l4cl li {
    width: 16.6666666667% !important; /* 6 products per row instead of 5 */
  }

  /* Adjust product image container to 380x330px max */
  .l4cl figure {
    max-width: 380px !important;
    margin: 0 auto 20px !important;
  }

  .l4cl figure picture {
    max-width: 380px !important;
    max-height: 330px !important;
    padding-top: 86.84% !important; /* 330/380 = 0.8684 for aspect ratio */
  }

  /* Ensure images fit properly without distortion */
  .l4cl figure img,
  .l4cl figure iframe,
  .l4cl figure video,
  .l4cl figure svg {
    object-fit: contain !important;
    object-position: center center !important;
    max-width: 380px !important;
    max-height: 330px !important;
  }

  /* Adjust container spacing to prevent gaps */
  .l4cl {
    --dist_a: 12px !important; /* Reduce gap between cards */
  }

  /* Ensure proper alignment and no overflow */
  .l4cl li {
    border-left-width: 12px !important;
    margin-bottom: 12px !important;
  }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .custom-product-title {
    font-size: 18px !important;
    line-height: 22px !important;
  }

  .custom-sale-price {
    font-size: 15px !important;
  }

  .custom-code-text {
    font-size: 14px !important;
    line-height: 19px !important;
  }

  .custom-price-container {
    gap: 3px !important;
  }
}

@media (max-width: 480px) {
  .custom-product-title {
    font-size: 16px !important;
    line-height: 20px !important;
  }

  .custom-sale-price {
    font-size: 14px !important;
  }

  .custom-code-text {
    font-size: 13px !important;
    line-height: 18px !important;
  }
}
